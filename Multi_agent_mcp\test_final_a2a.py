#!/usr/bin/env python3
"""
Final test script to verify the complete A2A format is working.
"""

import requests
import json
import time
import uuid

def test_complete_a2a_format():
    """Test the complete A2A message format with all required fields."""
    print("🔍 Testing Complete A2A Format...")
    
    # Prepare test payload with ALL required A2A fields
    test_payload = {
        "jsonrpc": "2.0",
        "method": "message/send",
        "params": {
            "task_id": str(uuid.uuid4()),
            "context_id": str(uuid.uuid4()),
            "user_id": "test_user_final",
            "timestamp": int(time.time() * 1000),
            "message": {
                "messageId": str(uuid.uuid4()),
                "role": "user",
                "parts": [
                    {
                        "text": "Test OCR processing with complete A2A format including all required fields"
                    }
                ],
                "metadata": {
                    "source": "final_test",
                    "description": "Complete A2A format test with all fields"
                }
            }
        },
        "id": str(uuid.uuid4())
    }
    
    print("✅ Complete A2A Request Format:")
    print(json.dumps(test_payload, indent=2))
    
    try:
        response = requests.post(
            "http://localhost:8003/a2a/ocr_agent",
            json=test_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"\n📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("📋 Response Body:")
            print(json.dumps(result, indent=2))
            
            if "error" not in result:
                print("\n✅ SUCCESS: Complete A2A request processed correctly!")
                return True
            else:
                print(f"\n❌ A2A Error: {result.get('error')}")
                return False
        else:
            print(f"\n❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"\n❌ Request Failed: {e}")
        return False

def show_expected_format():
    """Show the expected A2A format."""
    print("\n📋 Expected A2A Format:")
    print("=" * 30)
    
    expected_format = {
        "jsonrpc": "2.0",
        "method": "message/send",
        "params": {
            "task_id": "uuid-string",
            "context_id": "uuid-string", 
            "user_id": "user-string",
            "timestamp": 1234567890,
            "message": {
                "messageId": "uuid-string",
                "role": "user",
                "parts": [
                    {
                        "text": "message-content"
                    }
                ],
                "metadata": {
                    "key": "value"
                }
            }
        },
        "id": "uuid-string"
    }
    
    print(json.dumps(expected_format, indent=2))
    print("\n🔑 Required Fields:")
    print("- jsonrpc: '2.0'")
    print("- method: 'message/send'")
    print("- params.task_id: UUID string")
    print("- params.context_id: UUID string")
    print("- params.user_id: User identifier")
    print("- params.timestamp: Unix timestamp in milliseconds")
    print("- params.message.messageId: UUID string")
    print("- params.message.role: 'user' or 'assistant'")
    print("- params.message.parts: Array with text objects")
    print("- params.message.metadata: Optional metadata object")
    print("- id: Request ID (UUID string)")

def main():
    """Main test function."""
    print("🧪 Final A2A Format Testing")
    print("=" * 35)
    print("Testing the complete A2A format with all required fields.")
    print()
    
    # Show expected format first
    show_expected_format()
    
    # Test the complete A2A request
    success = test_complete_a2a_format()
    
    print("\n" + "=" * 35)
    print("📋 Final Test Result:")
    print(f"Complete A2A Request: {'✅ PASS' if success else '❌ FAIL'}")
    
    if success:
        print("\n🎉 A2A FORMAT IS NOW CORRECT!")
        print("✅ All required fields are present")
        print("✅ Message structure is valid")
        print("✅ OCR agent accepts the request")
        print("\n🚀 Now restart both services and test file upload in Postman!")
        print("\n📝 Restart Commands:")
        print("Terminal 1: cd Multi_agent_mcp/ocr_agent && python _main_.py --host localhost --port 8003")
        print("Terminal 2: cd Multi_agent_mcp && python file_upload_client.py --port 8004 --ocr-agent-url http://localhost:8003")
    else:
        print("\n❌ A2A format still has issues.")
        print("Check the error messages above for details.")
        print("The OCR agent may need additional configuration.")

if __name__ == "__main__":
    main()

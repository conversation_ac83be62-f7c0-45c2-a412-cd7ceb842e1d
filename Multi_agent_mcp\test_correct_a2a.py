#!/usr/bin/env python3
"""
Test script to verify the correct A2A format is working.
"""

import requests
import json
import time
import uuid

def test_correct_a2a_format():
    """Test the correct A2A message/send format."""
    print("🔍 Testing Correct A2A Format...")
    
    # Prepare test payload with correct A2A format
    test_payload = {
        "jsonrpc": "2.0",
        "method": "message/send",
        "params": {
            "task_id": str(uuid.uuid4()),
            "context_id": str(uuid.uuid4()),
            "user_id": "test_user_correct",
            "timestamp": int(time.time() * 1000),
            "message": {
                "content": "Test OCR processing with correct A2A format",
                "metadata": {
                    "source": "direct_test",
                    "description": "Correct A2A format test"
                }
            }
        },
        "id": str(uuid.uuid4())
    }
    
    print("✅ Correct A2A Request Format:")
    print(json.dumps(test_payload, indent=2))
    
    try:
        response = requests.post(
            "http://localhost:8003/a2a/ocr_agent",
            json=test_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"\n📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("📋 Response Body:")
            print(json.dumps(result, indent=2))
            
            if "error" not in result:
                print("\n✅ SUCCESS: A2A request processed correctly!")
                return True
            else:
                print(f"\n❌ A2A Error: {result.get('error')}")
                return False
        else:
            print(f"\n❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"\n❌ Request Failed: {e}")
        return False

def test_file_upload_with_correct_format():
    """Test file upload client after format fix."""
    print("\n📤 Testing File Upload Client with Correct Format...")
    
    # Create a simple test file
    import tempfile
    import os
    
    # Create a temporary test file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("This is a test file for OCR processing")
        temp_file_path = f.name
    
    try:
        # Test file upload
        with open(temp_file_path, 'rb') as f:
            files = {'files': ('test.txt', f, 'text/plain')}
            data = {
                'user_id': 'test_user_upload_correct',
                'description': 'Test upload with correct A2A format'
            }
            
            response = requests.post(
                "http://localhost:8004/upload",
                files=files,
                data=data,
                timeout=30
            )
        
        print(f"📊 Upload Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("📋 Upload Response:")
            print(json.dumps(result, indent=2))
            
            # Check if OCR result has proper format
            ocr_result = result.get('ocr_result', {})
            if 'error' not in ocr_result:
                print("\n✅ SUCCESS: File upload and OCR processing worked!")
                return True
            else:
                print(f"\n❌ OCR Error: {ocr_result.get('error')}")
                return False
        else:
            print(f"\n❌ Upload Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"\n❌ Upload Test Failed: {e}")
        return False
    finally:
        # Clean up temp file
        try:
            os.unlink(temp_file_path)
        except:
            pass

def main():
    """Main test function."""
    print("🧪 Correct A2A Format Testing")
    print("=" * 35)
    print("Testing the corrected A2A message/send format.")
    print()
    
    # Test 1: Direct A2A request
    direct_success = test_correct_a2a_format()
    
    # Test 2: File upload with corrected format
    upload_success = test_file_upload_with_correct_format()
    
    print("\n" + "=" * 35)
    print("📋 Final Test Results:")
    print(f"Direct A2A Request: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"File Upload Test: {'✅ PASS' if upload_success else '❌ FAIL'}")
    
    if direct_success and upload_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ A2A format is now correct")
        print("✅ File upload client is working")
        print("✅ OCR processing is functional")
        print("\n🚀 You can now test with real images in Postman!")
    else:
        print("\n❌ Some tests failed.")
        print("Check the error messages above for details.")
        if not direct_success:
            print("- Direct A2A format may still be incorrect")
        if not upload_success:
            print("- File upload client may have issues")

if __name__ == "__main__":
    main()

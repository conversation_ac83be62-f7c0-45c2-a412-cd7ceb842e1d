#!/usr/bin/env python3
"""
Test script to verify A2A JSON-RPC format is working correctly.
"""

import requests
import json
import time
import uuid

def test_direct_a2a_request():
    """Test direct A2A request to OCR agent."""
    print("🔍 Testing Direct A2A JSON-RPC Request...")
    
    # Prepare test payload in correct A2A JSON-RPC format
    test_payload = {
        "jsonrpc": "2.0",
        "method": "message/send",
        "params": {
            "task_id": str(uuid.uuid4()),
            "context_id": str(uuid.uuid4()),
            "user_id": "test_user_direct",
            "timestamp": int(time.time() * 1000),
            "message": {
                "content": "Test OCR processing with direct A2A request",
                "metadata": {
                    "source": "direct_test",
                    "description": "Direct A2A format test"
                }
            }
        },
        "id": str(uuid.uuid4())
    }
    
    print("Request payload:")
    print(json.dumps(test_payload, indent=2))
    
    try:
        response = requests.post(
            "http://localhost:8003/a2a/ocr_agent",
            json=test_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"\nResponse Status: {response.status_code}")
        print("Response Body:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 200:
            result = response.json()
            if "error" not in result:
                print("✅ Direct A2A request successful!")
                return True
            else:
                print(f"❌ A2A request returned error: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_file_upload_client():
    """Test file upload client with simple data."""
    print("\n📤 Testing File Upload Client...")
    
    # Create a simple test file content
    test_content = b"This is a test file for OCR processing"
    
    try:
        # Test with form data but no actual file
        response = requests.post(
            "http://localhost:8004/upload",
            data={
                "user_id": "test_user_upload",
                "description": "Test upload without file"
            },
            timeout=10
        )
        
        print(f"Response Status: {response.status_code}")
        print("Response Body:")
        print(json.dumps(response.json(), indent=2))
        
        if response.status_code == 400:
            print("✅ File upload validation working (correctly rejected empty upload)")
            return True
        else:
            print(f"⚠️  Unexpected response: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Upload test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 A2A Format Testing")
    print("=" * 30)
    print("Testing if the A2A JSON-RPC format is working correctly.")
    print()
    
    # Test 1: Direct A2A request
    direct_success = test_direct_a2a_request()
    
    # Test 2: File upload client
    upload_success = test_file_upload_client()
    
    print("\n" + "=" * 30)
    print("📋 Test Results:")
    print(f"Direct A2A Request: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"File Upload Client: {'✅ PASS' if upload_success else '❌ FAIL'}")
    
    if direct_success:
        print("\n🎉 A2A JSON-RPC format is working!")
        print("You can now test file uploads with actual images in Postman.")
    else:
        print("\n❌ A2A format issues detected.")
        print("Check the OCR agent server logs for more details.")

if __name__ == "__main__":
    main()
